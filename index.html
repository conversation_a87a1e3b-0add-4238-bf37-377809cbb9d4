<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sri <PERSON><PERSON> - Billing Software</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="company-info">
                <h1><i class="fas fa-industry"></i> Sri <PERSON><PERSON></h1>
                <p class="tagline">Professional Billing Solutions</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="clearBill()">
                    <i class="fas fa-plus"></i> New Bill
                </button>
                <button class="btn btn-primary" onclick="generatePDF()">
                    <i class="fas fa-download"></i> Download PDF
                </button>
            </div>
        </header>

        <!-- Bill Information -->
        <div class="bill-info">
            <div class="bill-header">
                <div class="bill-number">
                    <label for="billNo">Bill No:</label>
                    <input type="text" id="billNo" value="BILL-001" readonly>
                </div>
                <div class="bill-date">
                    <label for="billDate">Date:</label>
                    <input type="date" id="billDate">
                </div>
            </div>
            
            <div class="customer-info">
                <div class="form-group">
                    <label for="companyName">Company Name:</label>
                    <input type="text" id="companyName" placeholder="Enter company name">
                </div>
                <div class="form-group">
                    <label for="customerAddress">Address:</label>
                    <textarea id="customerAddress" placeholder="Enter customer address" rows="2"></textarea>
                </div>
            </div>
        </div>

        <!-- Billing Table -->
        <div class="billing-section">
            <div class="table-header">
                <h3><i class="fas fa-list"></i> Bill Items</h3>
                <button class="btn btn-success" onclick="addRow()">
                    <i class="fas fa-plus"></i> Add Item
                </button>
            </div>

            <div class="table-container">
                <table id="billingTable">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Particulars</th>
                            <th>No. of Pcs</th>
                            <th>Rate</th>
                            <th>Amount</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Dynamic rows will be added here -->
                    </tbody>
                </table>
            </div>

            <!-- Total Section -->
            <div class="total-section">
                <div class="total-row">
                    <span class="total-label">Total Amount:</span>
                    <span class="total-amount" id="totalAmount">₹0.00</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="saveBill()">
                <i class="fas fa-save"></i> Save Bill
            </button>
            <button class="btn btn-info" onclick="loadBills()">
                <i class="fas fa-history"></i> Load Previous Bills
            </button>
        </div>

        <!-- Bills History Modal -->
        <div id="billsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Previous Bills</h3>
                    <span class="close" onclick="closeBillsModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="billsList"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
