<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sri <PERSON><PERSON> - Billing Software</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="company-info">
                <h1><i class="fas fa-industry"></i> Sri <PERSON><PERSON></h1>
                <p class="tagline">Professional Billing Solutions</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="clearBill()">
                    <i class="fas fa-plus"></i> New Bill
                </button>
                <button class="btn btn-primary" onclick="generatePDF()">
                    <i class="fas fa-download"></i> Download PDF
                </button>
            </div>
        </header>

        <!-- Bill Format matching the image -->
        <div class="bill-format">
            <!-- Bill Header -->
            <div class="bill-header-section">
                <div class="labour-bill-header">
                    <span class="labour-bill-text">LABOUR BILL</span>
                    <span class="om-symbol">ॐ</span>
                    <span class="cell-number">Cell : 99765 04555</span>
                </div>

                <div class="company-header">
                    <h2>Sri Amman Fushing</h2>
                    <p class="company-address">65, N.P. Nagar 1st Street, Serankadu East, TIRUPUR - 641 604</p>
                </div>

                <div class="bill-details-row">
                    <div class="bill-number-section">
                        <span class="label">No.</span>
                        <input type="text" id="billNo" value="178" class="bill-input">
                    </div>
                    <div class="date-section">
                        <span class="label">Date</span>
                        <input type="date" id="billDate" class="bill-input">
                    </div>
                </div>

                <div class="customer-section">
                    <div class="to-section">
                        <span class="label">To,</span>
                    </div>
                    <div class="customer-name">
                        <span class="label">M/s.</span>
                        <input type="text" id="customerName" placeholder="Enter customer name" class="customer-input">
                    </div>
                </div>
            </div>
        </div>

        <!-- Billing Table matching image format -->
        <div class="billing-table-section">
            <div class="add-item-btn">
                <button class="btn btn-success" onclick="addRow()">
                    <i class="fas fa-plus"></i> Add Item
                </button>
            </div>

            <div class="bill-table-container">
                <table id="billingTable" class="bill-table">
                    <thead>
                        <tr>
                            <th class="sno-col">S.No</th>
                            <th class="particulars-col">Particulars</th>
                            <th class="pcs-col">No. of Pcs</th>
                            <th class="rate-col">Rate</th>
                            <th class="amount-col">Amount</th>
                            <th class="action-col">Action</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Dynamic rows will be added here -->
                    </tbody>
                </table>

                <!-- Total Section matching image -->
                <div class="total-section">
                    <div class="total-box">
                        <span class="total-label">Total</span>
                        <span class="total-amount" id="totalAmount">0.00</span>
                    </div>
                </div>

                <!-- Bottom section -->
                <div class="bill-footer">
                    <div class="eoe-section">
                        <span>E & O E</span>
                    </div>
                    <div class="for-company">
                        <span>For Sri Amman Fushing</span>
                    </div>
                    <div class="rupees-section">
                        <span class="rupees-label">Rupees</span>
                        <input type="text" id="amountInWords" class="rupees-input" readonly>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="saveBill()">
                <i class="fas fa-save"></i> Save Bill
            </button>
            <button class="btn btn-info" onclick="loadBills()">
                <i class="fas fa-history"></i> Load Previous Bills
            </button>
        </div>

        <!-- Bills History Modal -->
        <div id="billsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Previous Bills</h3>
                    <span class="close" onclick="closeBillsModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="billsList"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
