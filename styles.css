/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #f5f5f5;
    min-height: 100vh;
    color: #000;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.company-info h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.company-info h1 i {
    color: #3498db;
}

.tagline {
    color: #7f8c8d;
    font-style: italic;
    font-size: 1.1rem;
}

.header-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* <PERSON><PERSON> Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    padding: 8px 12px;
    font-size: 0.9rem;
}

/* Bill Format Styles matching the image */
.bill-format {
    background: white;
    border: 2px solid #000;
    margin-bottom: 20px;
    padding: 0;
    font-family: Arial, sans-serif;
}

.bill-header-section {
    border-bottom: 1px solid #000;
}

.labour-bill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    border-bottom: 1px solid #000;
    background: #f8f8f8;
}

.labour-bill-text {
    font-weight: bold;
    font-size: 14px;
}

.om-symbol {
    font-size: 18px;
    font-weight: bold;
}

.cell-number {
    font-size: 12px;
}

.company-header {
    text-align: center;
    padding: 10px;
    border-bottom: 1px solid #000;
}

.company-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    color: #000;
}

.company-address {
    margin: 5px 0 0 0;
    font-size: 12px;
    color: #000;
}

.bill-details-row {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    border-bottom: 1px solid #000;
}

.bill-number-section, .date-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.label {
    font-weight: bold;
    font-size: 14px;
}

.bill-input {
    border: none;
    border-bottom: 1px dotted #000;
    padding: 2px 5px;
    font-size: 14px;
    background: transparent;
    min-width: 100px;
}

.customer-section {
    padding: 10px 15px;
}

.to-section {
    margin-bottom: 5px;
}

.customer-name {
    display: flex;
    align-items: center;
    gap: 10px;
}

.customer-input {
    border: none;
    border-bottom: 1px dotted #000;
    padding: 2px 5px;
    font-size: 14px;
    background: transparent;
    flex: 1;
    min-width: 300px;
}

.bill-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.bill-number, .bill-date {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

label {
    font-weight: 600;
    color: #2c3e50;
}

input, textarea {
    padding: 12px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input:focus, textarea:focus {
    outline: none;
    border-color: #3498db;
}

/* Billing Table Section matching image */
.billing-table-section {
    background: white;
    border: 2px solid #000;
    border-top: none;
}

.add-item-btn {
    padding: 10px;
    text-align: center;
    background: #f8f8f8;
    border-bottom: 1px solid #000;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.table-header h3 {
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

.bill-table-container {
    padding: 0;
}

.bill-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.bill-table th {
    background: white;
    color: black;
    padding: 8px;
    text-align: center;
    font-weight: bold;
    border: 1px solid #000;
    font-size: 12px;
}

.bill-table td {
    padding: 8px;
    border: 1px solid #000;
    text-align: center;
    font-size: 12px;
    min-height: 30px;
}

/* Column widths matching the image */
.sno-col { width: 8%; }
.particulars-col { width: 40%; }
.pcs-col { width: 12%; }
.rate-col { width: 12%; }
.amount-col { width: 15%; }
.action-col { width: 13%; }

.table-input {
    width: 100%;
    padding: 4px;
    border: none;
    background: transparent;
    font-size: 12px;
    text-align: center;
}

.table-input:focus {
    outline: 1px solid #007bff;
    background: #f0f8ff;
}

/* Total Section matching image */
.total-section {
    display: flex;
    justify-content: flex-end;
    padding: 0;
    margin-top: 0;
}

.total-box {
    border: 1px solid #000;
    border-top: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120px;
}

.total-label {
    background: white;
    padding: 5px;
    border-bottom: 1px solid #000;
    width: 100%;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
}

.total-amount {
    padding: 10px;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    width: 100%;
}

/* Bill Footer matching image */
.bill-footer {
    padding: 15px;
    border-top: 1px solid #000;
}

.eoe-section {
    margin-bottom: 10px;
    font-size: 12px;
    font-weight: bold;
}

.for-company {
    text-align: right;
    margin-bottom: 15px;
    font-size: 12px;
    font-weight: bold;
}

.rupees-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rupees-label {
    font-weight: bold;
    font-size: 12px;
}

.rupees-input {
    border: none;
    border-bottom: 1px dotted #000;
    padding: 2px 5px;
    font-size: 12px;
    background: transparent;
    flex: 1;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ecf0f1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .company-info h1 {
        font-size: 2rem;
    }
    
    .customer-info {
        grid-template-columns: 1fr;
    }
    
    .bill-header {
        flex-direction: column;
    }
    
    .table-container {
        font-size: 0.9rem;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.container > * {
    animation: fadeIn 0.6s ease-out;
}
