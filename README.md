# Sri Amman Fushing - Billing Software

A professional billing software website with database connectivity and PDF download functionality.

## Features

- **Professional Design**: Modern, attractive, and responsive user interface
- **Database Connectivity**: SQLite database with Flask backend
- **Bill Management**: Create, save, and load bills
- **PDF Generation**: Download bills as PDF documents
- **Real-time Calculations**: Automatic amount calculations
- **Data Persistence**: Bills saved to both local storage and database
- **Company Branding**: Customized for Sri Amman Fushing

## Database Schema

### Bills Table
- `s_no` (int): Serial number (auto-increment)
- `particulars` (alphanumeric): Item description
- `no_of_pcs` (int): Number of pieces
- `rate` (float): Rate per piece
- `amount` (float): Total amount (calculated)

## Setup Instructions

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**:
   ```bash
   python app.py
   ```

3. **Access the Website**:
   Open your browser and go to: `http://localhost:5000`

## Usage

### Creating a Bill
1. Enter customer name and address
2. Click "Add Item" to add bill items
3. Fill in particulars, number of pieces, and rate
4. Amount is calculated automatically
5. Total is displayed at the bottom
6. Click "Save Bill" to save to database

### PDF Download
1. Fill in the bill details
2. Click "Download PDF" button
3. PDF will be generated and downloaded automatically

### Loading Previous Bills
1. Click "Load Previous Bills" button
2. Select a bill from the list
3. Bill data will be loaded into the form

### Features
- **Auto-calculation**: Amount and total are calculated automatically
- **Data validation**: Required fields are validated before saving
- **Responsive design**: Works on desktop and mobile devices
- **Professional styling**: Modern gradient design with animations

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality
├── app.py              # Flask backend server
├── requirements.txt    # Python dependencies
├── README.md           # This file
└── billing.db          # SQLite database (created automatically)
```

## API Endpoints

- `POST /api/save-bill` - Save a new bill
- `GET /api/get-bills` - Get all bills
- `GET /api/get-bill/<bill_no>` - Get specific bill
- `DELETE /api/delete-bill/<bill_no>` - Delete a bill
- `GET /api/stats` - Get billing statistics

## Technologies Used

- **Frontend**: HTML5, CSS3, JavaScript
- **Backend**: Python Flask
- **Database**: SQLite
- **PDF Generation**: jsPDF library
- **Styling**: Custom CSS with gradients and animations
- **Icons**: Font Awesome

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Support

For any issues or questions, please contact the development team.

---

**Sri Amman Fushing** - Professional Billing Solutions
