// Global variables
let billCounter = 1;
let currentBillData = {
    billNo: '',
    date: '',
    customerName: '',
    customerAddress: '',
    items: [],
    total: 0
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Set current date
    document.getElementById('billDate').value = new Date().toISOString().split('T')[0];
    
    // Generate initial bill number
    generateBillNumber();
    
    // Add initial row
    addRow();
    
    // Load bill counter from localStorage
    const savedCounter = localStorage.getItem('billCounter');
    if (savedCounter) {
        billCounter = parseInt(savedCounter);
        generateBillNumber();
    }
});

// Generate bill number
function generateBillNumber() {
    const billNo = `BILL-${String(billCounter).padStart(3, '0')}`;
    document.getElementById('billNo').value = billNo;
    currentBillData.billNo = billNo;
}

// Add new row to the table
function addRow() {
    const tableBody = document.getElementById('tableBody');
    const rowCount = tableBody.rows.length + 1;
    
    const row = tableBody.insertRow();
    row.innerHTML = `
        <td>${rowCount}</td>
        <td><input type="text" class="table-input" placeholder="Enter item description" onchange="updateRowData(this)"></td>
        <td><input type="number" class="table-input" min="1" value="1" onchange="calculateAmount(this)"></td>
        <td><input type="number" class="table-input" min="0" step="0.01" placeholder="0.00" onchange="calculateAmount(this)"></td>
        <td class="amount-cell">₹0.00</td>
        <td><button class="btn btn-danger" onclick="removeRow(this)"><i class="fas fa-trash"></i></button></td>
    `;
    
    // Focus on the particulars input
    row.cells[1].querySelector('input').focus();
}

// Remove row from table
function removeRow(button) {
    const row = button.closest('tr');
    row.remove();
    updateSerialNumbers();
    calculateTotal();
}

// Update serial numbers after row removal
function updateSerialNumbers() {
    const tableBody = document.getElementById('tableBody');
    for (let i = 0; i < tableBody.rows.length; i++) {
        tableBody.rows[i].cells[0].textContent = i + 1;
    }
}

// Calculate amount for a row
function calculateAmount(input) {
    const row = input.closest('tr');
    const pcsInput = row.cells[2].querySelector('input');
    const rateInput = row.cells[3].querySelector('input');
    const amountCell = row.cells[4];
    
    const pcs = parseFloat(pcsInput.value) || 0;
    const rate = parseFloat(rateInput.value) || 0;
    const amount = pcs * rate;
    
    amountCell.textContent = `₹${amount.toFixed(2)}`;
    calculateTotal();
}

// Calculate total amount
function calculateTotal() {
    const tableBody = document.getElementById('tableBody');
    let total = 0;
    
    for (let i = 0; i < tableBody.rows.length; i++) {
        const amountText = tableBody.rows[i].cells[4].textContent;
        const amount = parseFloat(amountText.replace('₹', '')) || 0;
        total += amount;
    }
    
    document.getElementById('totalAmount').textContent = `₹${total.toFixed(2)}`;
    currentBillData.total = total;
}

// Update row data
function updateRowData(input) {
    calculateTotal();
}

// Clear bill and start new
function clearBill() {
    if (confirm('Are you sure you want to clear the current bill?')) {
        // Clear customer info
        document.getElementById('customerName').value = '';
        document.getElementById('customerAddress').value = '';
        
        // Clear table
        document.getElementById('tableBody').innerHTML = '';
        
        // Reset total
        document.getElementById('totalAmount').textContent = '₹0.00';
        
        // Generate new bill number
        billCounter++;
        localStorage.setItem('billCounter', billCounter);
        generateBillNumber();
        
        // Add initial row
        addRow();
        
        // Set current date
        document.getElementById('billDate').value = new Date().toISOString().split('T')[0];
    }
}

// Save bill to localStorage
function saveBill() {
    // Validate required fields
    const customerName = document.getElementById('customerName').value.trim();
    if (!customerName) {
        alert('Please enter customer name');
        return;
    }
    
    // Collect bill data
    const billData = {
        billNo: document.getElementById('billNo').value,
        date: document.getElementById('billDate').value,
        customerName: customerName,
        customerAddress: document.getElementById('customerAddress').value,
        items: [],
        total: 0
    };
    
    // Collect items data
    const tableBody = document.getElementById('tableBody');
    for (let i = 0; i < tableBody.rows.length; i++) {
        const row = tableBody.rows[i];
        const particulars = row.cells[1].querySelector('input').value.trim();
        const pcs = parseInt(row.cells[2].querySelector('input').value) || 0;
        const rate = parseFloat(row.cells[3].querySelector('input').value) || 0;
        const amount = pcs * rate;
        
        if (particulars && pcs > 0 && rate > 0) {
            billData.items.push({
                sno: i + 1,
                particulars: particulars,
                pcs: pcs,
                rate: rate,
                amount: amount
            });
        }
    }
    
    if (billData.items.length === 0) {
        alert('Please add at least one item to the bill');
        return;
    }
    
    billData.total = billData.items.reduce((sum, item) => sum + item.amount, 0);
    
    // Save to localStorage
    const savedBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
    savedBills.push(billData);
    localStorage.setItem('savedBills', JSON.stringify(savedBills));
    
    alert('Bill saved successfully!');
    
    // Send to backend (if available)
    saveBillToDatabase(billData);
}

// Save bill to database (backend API)
async function saveBillToDatabase(billData) {
    try {
        const response = await fetch('/api/save-bill', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(billData)
        });
        
        if (response.ok) {
            console.log('Bill saved to database successfully');
        }
    } catch (error) {
        console.log('Database not available, using local storage only');
    }
}

// Load bills from database and localStorage
async function loadBills() {
    const billsList = document.getElementById('billsList');
    billsList.innerHTML = '<p>Loading bills...</p>';

    let allBills = [];

    // Try to load from database first
    try {
        const response = await fetch('/api/get-bills');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                allBills = data.bills;
            }
        }
    } catch (error) {
        console.log('Database not available, loading from local storage');
    }

    // If no bills from database, load from localStorage
    if (allBills.length === 0) {
        allBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
    }

    if (allBills.length === 0) {
        billsList.innerHTML = '<p>No saved bills found.</p>';
    } else {
        billsList.innerHTML = allBills.map(bill => `
            <div class="bill-item" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; cursor: pointer; position: relative;" onclick="loadBill('${bill.billNo}')">
                <h4>${bill.billNo} - ${bill.customerName}</h4>
                <p>Date: ${bill.date} | Total: ₹${bill.total.toFixed(2)}</p>
                <p>Items: ${bill.items.length}</p>
                <button class="btn btn-danger" style="position: absolute; top: 10px; right: 10px; padding: 5px 10px; font-size: 0.8rem;" onclick="event.stopPropagation(); deleteBill('${bill.billNo}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    document.getElementById('billsModal').style.display = 'block';
}

// Load specific bill
async function loadBill(billNo) {
    let bill = null;

    // Try to load from database first
    try {
        const response = await fetch(`/api/get-bill/${billNo}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                bill = data.bill;
            }
        }
    } catch (error) {
        console.log('Database not available, loading from local storage');
    }

    // If not found in database, load from localStorage
    if (!bill) {
        const savedBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
        bill = savedBills.find(b => b.billNo === billNo);
    }

    if (bill) {
        // Load bill data
        document.getElementById('billNo').value = bill.billNo;
        document.getElementById('billDate').value = bill.date;
        document.getElementById('customerName').value = bill.customerName;
        document.getElementById('customerAddress').value = bill.customerAddress || '';

        // Clear existing rows
        document.getElementById('tableBody').innerHTML = '';

        // Load items
        bill.items.forEach(item => {
            const tableBody = document.getElementById('tableBody');
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td>${item.sno}</td>
                <td><input type="text" class="table-input" value="${item.particulars}" onchange="updateRowData(this)"></td>
                <td><input type="number" class="table-input" min="1" value="${item.pcs}" onchange="calculateAmount(this)"></td>
                <td><input type="number" class="table-input" min="0" step="0.01" value="${item.rate}" onchange="calculateAmount(this)"></td>
                <td class="amount-cell">₹${item.amount.toFixed(2)}</td>
                <td><button class="btn btn-danger" onclick="removeRow(this)"><i class="fas fa-trash"></i></button></td>
            `;
        });

        calculateTotal();
        closeBillsModal();
    }
}

// Delete bill
async function deleteBill(billNo) {
    if (!confirm(`Are you sure you want to delete bill ${billNo}?`)) {
        return;
    }

    // Try to delete from database first
    try {
        const response = await fetch(`/api/delete-bill/${billNo}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                alert('Bill deleted successfully from database');
            }
        }
    } catch (error) {
        console.log('Database not available, deleting from local storage only');
    }

    // Also delete from localStorage
    const savedBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
    const updatedBills = savedBills.filter(bill => bill.billNo !== billNo);
    localStorage.setItem('savedBills', JSON.stringify(updatedBills));

    // Refresh the bills list
    loadBills();
}

// Close bills modal
function closeBillsModal() {
    document.getElementById('billsModal').style.display = 'none';
}

// Generate PDF
function generatePDF() {
    const customerName = document.getElementById('customerName').value.trim();
    if (!customerName) {
        alert('Please enter customer name before generating PDF');
        return;
    }
    
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    
    // Company header
    doc.setFontSize(20);
    doc.setTextColor(44, 62, 80);
    doc.text('Sri Amman Fushing', 20, 30);
    
    doc.setFontSize(12);
    doc.setTextColor(127, 140, 141);
    doc.text('Professional Billing Solutions', 20, 40);
    
    // Bill information
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.text(`Bill No: ${document.getElementById('billNo').value}`, 20, 60);
    doc.text(`Date: ${document.getElementById('billDate').value}`, 120, 60);
    
    doc.text(`Customer: ${customerName}`, 20, 75);
    const address = document.getElementById('customerAddress').value;
    if (address) {
        doc.text(`Address: ${address}`, 20, 85);
    }
    
    // Table data
    const tableBody = document.getElementById('tableBody');
    const tableData = [];
    
    for (let i = 0; i < tableBody.rows.length; i++) {
        const row = tableBody.rows[i];
        const particulars = row.cells[1].querySelector('input').value;
        const pcs = row.cells[2].querySelector('input').value;
        const rate = row.cells[3].querySelector('input').value;
        const amount = row.cells[4].textContent;
        
        if (particulars.trim()) {
            tableData.push([i + 1, particulars, pcs, `₹${rate}`, amount]);
        }
    }
    
    // Add table
    doc.autoTable({
        head: [['S.No', 'Particulars', 'No. of Pcs', 'Rate', 'Amount']],
        body: tableData,
        startY: address ? 95 : 85,
        theme: 'grid',
        headStyles: {
            fillColor: [52, 73, 94],
            textColor: [255, 255, 255],
            fontStyle: 'bold'
        },
        styles: {
            fontSize: 10,
            cellPadding: 5
        }
    });
    
    // Total
    const finalY = doc.lastAutoTable.finalY + 10;
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text(`Total Amount: ${document.getElementById('totalAmount').textContent}`, 120, finalY);
    
    // Save PDF
    const billNo = document.getElementById('billNo').value;
    doc.save(`${billNo}_${customerName.replace(/\s+/g, '_')}.pdf`);
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('billsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
