// Global variables
let billCounter = 1;
let currentBillData = {
    billNo: '',
    date: '',
    customerName: '',
    customerAddress: '',
    items: [],
    total: 0
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Set current date
    document.getElementById('billDate').value = new Date().toISOString().split('T')[0];
    
    // Generate initial bill number
    generateBillNumber();
    
    // Add initial row
    addRow();
    
    // Load bill counter from localStorage
    const savedCounter = localStorage.getItem('billCounter');
    if (savedCounter) {
        billCounter = parseInt(savedCounter);
        generateBillNumber();
    }
});

// Generate bill number
function generateBillNumber() {
    const billNo = String(billCounter).padStart(3, '0');
    document.getElementById('billNo').value = billNo;
    currentBillData.billNo = billNo;
}

// Convert number to words
function numberToWords(num) {
    if (num === 0) return 'Zero';

    const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

    function convertHundreds(n) {
        let result = '';
        if (n >= 100) {
            result += ones[Math.floor(n / 100)] + ' Hundred ';
            n %= 100;
        }
        if (n >= 20) {
            result += tens[Math.floor(n / 10)] + ' ';
            n %= 10;
        } else if (n >= 10) {
            result += teens[n - 10] + ' ';
            return result;
        }
        if (n > 0) {
            result += ones[n] + ' ';
        }
        return result;
    }

    if (num >= 10000000) { // Crores
        return convertHundreds(Math.floor(num / 10000000)) + 'Crore ' + numberToWords(num % 10000000);
    } else if (num >= 100000) { // Lakhs
        return convertHundreds(Math.floor(num / 100000)) + 'Lakh ' + numberToWords(num % 100000);
    } else if (num >= 1000) { // Thousands
        return convertHundreds(Math.floor(num / 1000)) + 'Thousand ' + numberToWords(num % 1000);
    } else {
        return convertHundreds(num);
    }
}

// Add new row to the table
function addRow() {
    const tableBody = document.getElementById('tableBody');
    const rowCount = tableBody.rows.length + 1;
    
    const row = tableBody.insertRow();
    row.innerHTML = `
        <td>${rowCount}</td>
        <td><input type="text" class="table-input" placeholder="Enter item description" onchange="updateRowData(this)" style="text-align: left;"></td>
        <td><input type="number" class="table-input" min="1" value="1" onchange="calculateAmount(this)"></td>
        <td><input type="number" class="table-input" min="0" step="0.01" placeholder="0.00" onchange="calculateAmount(this)"></td>
        <td class="amount-cell">0.00</td>
        <td><button class="btn btn-danger" onclick="removeRow(this)"><i class="fas fa-trash"></i></button></td>
    `;
    
    // Focus on the particulars input
    row.cells[1].querySelector('input').focus();
}

// Remove row from table
function removeRow(button) {
    const row = button.closest('tr');
    row.remove();
    updateSerialNumbers();
    calculateTotal();
}

// Update serial numbers after row removal
function updateSerialNumbers() {
    const tableBody = document.getElementById('tableBody');
    for (let i = 0; i < tableBody.rows.length; i++) {
        tableBody.rows[i].cells[0].textContent = i + 1;
    }
}

// Calculate amount for a row
function calculateAmount(input) {
    const row = input.closest('tr');
    const pcsInput = row.cells[2].querySelector('input');
    const rateInput = row.cells[3].querySelector('input');
    const amountCell = row.cells[4];

    const pcs = parseFloat(pcsInput.value) || 0;
    const rate = parseFloat(rateInput.value) || 0;
    const amount = pcs * rate;

    amountCell.textContent = amount.toFixed(2);
    calculateTotal();
}

// Calculate total amount
function calculateTotal() {
    const tableBody = document.getElementById('tableBody');
    let total = 0;

    for (let i = 0; i < tableBody.rows.length; i++) {
        const amountText = tableBody.rows[i].cells[4].textContent;
        const amount = parseFloat(amountText) || 0;
        total += amount;
    }

    document.getElementById('totalAmount').textContent = total.toFixed(2);

    // Update amount in words
    const totalInWords = numberToWords(Math.floor(total));
    const paise = Math.round((total - Math.floor(total)) * 100);
    let amountInWords = totalInWords + 'Rupees';
    if (paise > 0) {
        amountInWords += ' and ' + numberToWords(paise) + 'Paise';
    }
    amountInWords += ' Only';
    document.getElementById('amountInWords').value = amountInWords;

    currentBillData.total = total;
}

// Update row data
function updateRowData(input) {
    calculateTotal();
}

// Clear bill and start new
function clearBill() {
    if (confirm('Are you sure you want to clear the current bill?')) {
        // Clear customer info
        document.getElementById('customerName').value = '';
        document.getElementById('customerAddress').value = '';
        
        // Clear table
        document.getElementById('tableBody').innerHTML = '';
        
        // Reset total
        document.getElementById('totalAmount').textContent = '0.00';
        document.getElementById('amountInWords').value = '';
        
        // Generate new bill number
        billCounter++;
        localStorage.setItem('billCounter', billCounter);
        generateBillNumber();
        
        // Add initial row
        addRow();
        
        // Set current date
        document.getElementById('billDate').value = new Date().toISOString().split('T')[0];
    }
}

// Save bill to localStorage
function saveBill() {
    // Validate required fields
    const customerName = document.getElementById('customerName').value.trim();
    if (!customerName) {
        alert('Please enter customer name');
        return;
    }
    
    // Collect bill data
    const billData = {
        billNo: document.getElementById('billNo').value,
        date: document.getElementById('billDate').value,
        customerName: customerName,
        customerAddress: document.getElementById('customerAddress').value,
        items: [],
        total: 0
    };
    
    // Collect items data
    const tableBody = document.getElementById('tableBody');
    for (let i = 0; i < tableBody.rows.length; i++) {
        const row = tableBody.rows[i];
        const particulars = row.cells[1].querySelector('input').value.trim();
        const pcs = parseInt(row.cells[2].querySelector('input').value) || 0;
        const rate = parseFloat(row.cells[3].querySelector('input').value) || 0;
        const amount = pcs * rate;
        
        if (particulars && pcs > 0 && rate > 0) {
            billData.items.push({
                sno: i + 1,
                particulars: particulars,
                pcs: pcs,
                rate: rate,
                amount: amount
            });
        }
    }
    
    if (billData.items.length === 0) {
        alert('Please add at least one item to the bill');
        return;
    }
    
    billData.total = billData.items.reduce((sum, item) => sum + item.amount, 0);
    
    // Save to localStorage
    const savedBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
    savedBills.push(billData);
    localStorage.setItem('savedBills', JSON.stringify(savedBills));
    
    alert('Bill saved successfully!');
    
    // Send to backend (if available)
    saveBillToDatabase(billData);
}

// Save bill to database (backend API)
async function saveBillToDatabase(billData) {
    try {
        const response = await fetch('/api/save-bill', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(billData)
        });
        
        if (response.ok) {
            console.log('Bill saved to database successfully');
        }
    } catch (error) {
        console.log('Database not available, using local storage only');
    }
}

// Load bills from database and localStorage
async function loadBills() {
    const billsList = document.getElementById('billsList');
    billsList.innerHTML = '<p>Loading bills...</p>';

    let allBills = [];

    // Try to load from database first
    try {
        const response = await fetch('/api/get-bills');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                allBills = data.bills;
            }
        }
    } catch (error) {
        console.log('Database not available, loading from local storage');
    }

    // If no bills from database, load from localStorage
    if (allBills.length === 0) {
        allBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
    }

    if (allBills.length === 0) {
        billsList.innerHTML = '<p>No saved bills found.</p>';
    } else {
        billsList.innerHTML = allBills.map(bill => `
            <div class="bill-item" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; cursor: pointer; position: relative;" onclick="loadBill('${bill.billNo}')">
                <h4>${bill.billNo} - ${bill.customerName}</h4>
                <p>Date: ${bill.date} | Total: ₹${bill.total.toFixed(2)}</p>
                <p>Items: ${bill.items.length}</p>
                <button class="btn btn-danger" style="position: absolute; top: 10px; right: 10px; padding: 5px 10px; font-size: 0.8rem;" onclick="event.stopPropagation(); deleteBill('${bill.billNo}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    document.getElementById('billsModal').style.display = 'block';
}

// Load specific bill
async function loadBill(billNo) {
    let bill = null;

    // Try to load from database first
    try {
        const response = await fetch(`/api/get-bill/${billNo}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                bill = data.bill;
            }
        }
    } catch (error) {
        console.log('Database not available, loading from local storage');
    }

    // If not found in database, load from localStorage
    if (!bill) {
        const savedBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
        bill = savedBills.find(b => b.billNo === billNo);
    }

    if (bill) {
        // Load bill data
        document.getElementById('billNo').value = bill.billNo;
        document.getElementById('billDate').value = bill.date;
        document.getElementById('customerName').value = bill.customerName;
        document.getElementById('customerAddress').value = bill.customerAddress || '';

        // Clear existing rows
        document.getElementById('tableBody').innerHTML = '';

        // Load items
        bill.items.forEach(item => {
            const tableBody = document.getElementById('tableBody');
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td>${item.sno}</td>
                <td><input type="text" class="table-input" value="${item.particulars}" onchange="updateRowData(this)" style="text-align: left;"></td>
                <td><input type="number" class="table-input" min="1" value="${item.pcs}" onchange="calculateAmount(this)"></td>
                <td><input type="number" class="table-input" min="0" step="0.01" value="${item.rate}" onchange="calculateAmount(this)"></td>
                <td class="amount-cell">${item.amount.toFixed(2)}</td>
                <td><button class="btn btn-danger" onclick="removeRow(this)"><i class="fas fa-trash"></i></button></td>
            `;
        });

        calculateTotal();
        closeBillsModal();
    }
}

// Delete bill
async function deleteBill(billNo) {
    if (!confirm(`Are you sure you want to delete bill ${billNo}?`)) {
        return;
    }

    // Try to delete from database first
    try {
        const response = await fetch(`/api/delete-bill/${billNo}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                alert('Bill deleted successfully from database');
            }
        }
    } catch (error) {
        console.log('Database not available, deleting from local storage only');
    }

    // Also delete from localStorage
    const savedBills = JSON.parse(localStorage.getItem('savedBills') || '[]');
    const updatedBills = savedBills.filter(bill => bill.billNo !== billNo);
    localStorage.setItem('savedBills', JSON.stringify(updatedBills));

    // Refresh the bills list
    loadBills();
}

// Close bills modal
function closeBillsModal() {
    document.getElementById('billsModal').style.display = 'none';
}

// Generate PDF matching the bill format
function generatePDF() {
    const customerName = document.getElementById('customerName').value.trim();
    if (!customerName) {
        alert('Please enter customer name before generating PDF');
        return;
    }

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Draw border
    doc.rect(10, 10, 190, 270);

    // Labour Bill header
    doc.setFontSize(12);
    doc.text('LABOUR BILL', 15, 25);

    // Create Om symbol using canvas and add as image
    const canvas = document.createElement('canvas');
    canvas.width = 20;
    canvas.height = 20;
    const ctx = canvas.getContext('2d');

    // Draw Om symbol on canvas
    ctx.fillStyle = 'black';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';

    // Try to draw the Om symbol
    try {
        ctx.fillText('ॐ', 10, 15);

        // Convert canvas to image and add to PDF
        const imgData = canvas.toDataURL('image/png');
        doc.addImage(imgData, 'PNG', 98, 20, 6, 6);
    } catch (error) {
        // Fallback: Draw a simple geometric Om representation
        doc.setLineWidth(0.8);
        doc.setDrawColor(0, 0, 0);

        // Main curve of Om
        doc.arc(100, 24, 2, 0.3, Math.PI - 0.3, 'S');

        // Upper curve
        doc.arc(101.5, 22, 1.2, Math.PI + 0.2, 2 * Math.PI - 0.2, 'S');

        // Connecting elements
        doc.line(99.2, 22.2, 100.8, 22.2);

        // Dot
        doc.circle(100.8, 20, 0.3, 'F');

        // Crescent
        doc.arc(100.8, 19, 0.6, 0.2, Math.PI - 0.2, 'S');

        // Tail
        doc.line(98.2, 24.8, 98.2, 26.2);
        doc.arc(98.6, 26.2, 0.4, Math.PI, 1.5 * Math.PI, 'S');
    }

    doc.setFontSize(12);
    doc.text('Cell : 99765 04555', 150, 25);
    doc.line(10, 28, 200, 28);

    // Company name and address
    doc.setFontSize(18);
    doc.setFont(undefined, 'bold');
    doc.text('Sri Amman Fushing', 105, 40, { align: 'center' });

    doc.setFontSize(10);
    doc.setFont(undefined, 'normal');
    doc.text('65, N.P. Nagar 1st Street, Serankadu East, TIRUPUR - 641 604', 105, 48, { align: 'center' });
    doc.line(10, 52, 200, 52);

    // Bill details
    doc.setFontSize(12);
    doc.text(`No. ${document.getElementById('billNo').value}`, 15, 65);
    doc.text(`Date ${document.getElementById('billDate').value}`, 150, 65);
    doc.line(10, 68, 200, 68);

    // Customer details
    doc.text('To,', 15, 78);
    doc.text(`M/s. ${customerName}`, 15, 88);
    doc.line(10, 92, 200, 92);

    // Table data
    const tableBody = document.getElementById('tableBody');
    const tableData = [];

    for (let i = 0; i < tableBody.rows.length; i++) {
        const row = tableBody.rows[i];
        const particulars = row.cells[1].querySelector('input').value;
        const pcs = row.cells[2].querySelector('input').value;
        const rate = row.cells[3].querySelector('input').value;
        const amount = row.cells[4].textContent;

        if (particulars.trim()) {
            tableData.push([i + 1, particulars, pcs, rate, amount]);
        }
    }

    // Add table
    doc.autoTable({
        head: [['S.No', 'Particulars', 'No. of Pcs', 'Rate', 'Amount']],
        body: tableData,
        startY: 95,
        theme: 'grid',
        styles: {
            fontSize: 10,
            cellPadding: 3,
            lineColor: [0, 0, 0],
            lineWidth: 0.5
        },
        headStyles: {
            fillColor: [255, 255, 255],
            textColor: [0, 0, 0],
            fontStyle: 'bold'
        },
        margin: { left: 10, right: 10 }
    });

    // Total section
    const finalY = doc.lastAutoTable.finalY;
    doc.rect(160, finalY, 40, 20);
    doc.text('Total', 180, finalY + 8, { align: 'center' });
    doc.line(160, finalY + 10, 200, finalY + 10);
    doc.text(document.getElementById('totalAmount').textContent, 180, finalY + 18, { align: 'center' });

    // Footer
    doc.text('E & O E', 15, finalY + 30);
    doc.text('For Sri Amman Fushing', 150, finalY + 30);

    // Add more space between "For Sri Amman Fushing" and "Rupees"
    doc.text(`Rupees ${document.getElementById('amountInWords').value}`, 15, finalY + 55);

    // Save PDF
    const billNo = document.getElementById('billNo').value;
    doc.save(`Bill_${billNo}_${customerName.replace(/\s+/g, '_')}.pdf`);
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('billsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
