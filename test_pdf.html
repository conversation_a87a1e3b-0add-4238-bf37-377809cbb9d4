<!DOCTYPE html>
<html>
<head>
    <title>PDF Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <h1>PDF Generation Test</h1>
    <button onclick="testPDF()">Test PDF with Om Symbol</button>
    
    <script>
        function testPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Test Om symbol rendering
            doc.setFontSize(12);
            doc.text('LABOUR BILL', 15, 25);
            doc.setFontSize(16);
            doc.text('ॐ', 100, 25);
            doc.setFontSize(12);
            doc.text('Cell : 99765 04555', 150, 25);
            
            doc.save('test_om_symbol.pdf');
        }
    </script>
</body>
</html>
