<!DOCTYPE html>
<html>
<head>
    <title>PDF Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <h1>PDF Generation Test</h1>
    <button onclick="testPDF()">Test PDF with Om Symbol</button>
    
    <script>
        function testPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Test Om symbol rendering with shapes
            doc.setFontSize(12);
            doc.text('LABOUR BILL', 15, 25);

            // Draw Om symbol using basic shapes
            doc.setLineWidth(0.5);

            // Main body of Om (large curve)
            doc.arc(100, 24, 2.5, 0.2, Math.PI - 0.2, 'S');

            // Upper part (smaller curve)
            doc.arc(102, 22, 1.5, Math.PI + 0.3, 2 * Math.PI - 0.3, 'S');

            // Connecting line
            doc.line(99.5, 21.8, 101, 21.8);

            // Dot above
            doc.circle(101, 19.5, 0.4, 'F');

            // Crescent above dot
            doc.arc(101, 18.5, 0.8, 0.3, Math.PI - 0.3, 'S');

            // Tail of Om
            doc.line(97.8, 24.5, 97.8, 26);
            doc.arc(98.3, 26, 0.5, Math.PI, 1.5 * Math.PI, 'S');

            doc.setFontSize(12);
            doc.text('Cell : 99765 04555', 150, 25);

            doc.save('test_om_symbol.pdf');
        }
    </script>
</body>
</html>
