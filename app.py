from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import sqlite3
import json
from datetime import datetime
import os

app = Flask(__name__)
CORS(app)

# Database setup
def init_db():
    conn = sqlite3.connect('billing.db')
    cursor = conn.cursor()
    
    # Create bills table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS bills (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bill_no TEXT UNIQUE NOT NULL,
            date TEXT NOT NULL,
            customer_name TEXT NOT NULL,
            customer_address TEXT,
            total_amount REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create bill_items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS bill_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bill_id INTEGER,
            s_no INTEGER NOT NULL,
            particulars TEXT NOT NULL,
            no_of_pcs INTEGER NOT NULL,
            rate REAL NOT NULL,
            amount REAL NOT NULL,
            FOREIGN KEY (bill_id) REFERENCES bills (id)
        )
    ''')
    
    conn.commit()
    conn.close()

# Initialize database on startup
init_db()

# Serve static files
@app.route('/')
def serve_index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    return send_from_directory('.', filename)

# API Routes
@app.route('/api/save-bill', methods=['POST'])
def save_bill():
    try:
        bill_data = request.json
        
        conn = sqlite3.connect('billing.db')
        cursor = conn.cursor()
        
        # Insert bill
        cursor.execute('''
            INSERT INTO bills (bill_no, date, customer_name, customer_address, total_amount)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            bill_data['billNo'],
            bill_data['date'],
            bill_data['customerName'],
            bill_data['customerAddress'],
            bill_data['total']
        ))
        
        bill_id = cursor.lastrowid
        
        # Insert bill items
        for item in bill_data['items']:
            cursor.execute('''
                INSERT INTO bill_items (bill_id, s_no, particulars, no_of_pcs, rate, amount)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                bill_id,
                item['sno'],
                item['particulars'],
                item['pcs'],
                item['rate'],
                item['amount']
            ))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': 'Bill saved successfully'})
    
    except sqlite3.IntegrityError:
        return jsonify({'success': False, 'message': 'Bill number already exists'}), 400
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/get-bills', methods=['GET'])
def get_bills():
    try:
        conn = sqlite3.connect('billing.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, bill_no, date, customer_name, customer_address, total_amount, created_at
            FROM bills
            ORDER BY created_at DESC
        ''')
        
        bills = []
        for row in cursor.fetchall():
            bill = {
                'id': row[0],
                'billNo': row[1],
                'date': row[2],
                'customerName': row[3],
                'customerAddress': row[4],
                'total': row[5],
                'createdAt': row[6]
            }
            
            # Get bill items
            cursor.execute('''
                SELECT s_no, particulars, no_of_pcs, rate, amount
                FROM bill_items
                WHERE bill_id = ?
                ORDER BY s_no
            ''', (row[0],))
            
            bill['items'] = []
            for item_row in cursor.fetchall():
                bill['items'].append({
                    'sno': item_row[0],
                    'particulars': item_row[1],
                    'pcs': item_row[2],
                    'rate': item_row[3],
                    'amount': item_row[4]
                })
            
            bills.append(bill)
        
        conn.close()
        return jsonify({'success': True, 'bills': bills})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/get-bill/<bill_no>', methods=['GET'])
def get_bill(bill_no):
    try:
        conn = sqlite3.connect('billing.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, bill_no, date, customer_name, customer_address, total_amount, created_at
            FROM bills
            WHERE bill_no = ?
        ''', (bill_no,))
        
        row = cursor.fetchone()
        if not row:
            return jsonify({'success': False, 'message': 'Bill not found'}), 404
        
        bill = {
            'id': row[0],
            'billNo': row[1],
            'date': row[2],
            'customerName': row[3],
            'customerAddress': row[4],
            'total': row[5],
            'createdAt': row[6]
        }
        
        # Get bill items
        cursor.execute('''
            SELECT s_no, particulars, no_of_pcs, rate, amount
            FROM bill_items
            WHERE bill_id = ?
            ORDER BY s_no
        ''', (row[0],))
        
        bill['items'] = []
        for item_row in cursor.fetchall():
            bill['items'].append({
                'sno': item_row[0],
                'particulars': item_row[1],
                'pcs': item_row[2],
                'rate': item_row[3],
                'amount': item_row[4]
            })
        
        conn.close()
        return jsonify({'success': True, 'bill': bill})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/delete-bill/<bill_no>', methods=['DELETE'])
def delete_bill(bill_no):
    try:
        conn = sqlite3.connect('billing.db')
        cursor = conn.cursor()
        
        # Get bill ID
        cursor.execute('SELECT id FROM bills WHERE bill_no = ?', (bill_no,))
        row = cursor.fetchone()
        if not row:
            return jsonify({'success': False, 'message': 'Bill not found'}), 404
        
        bill_id = row[0]
        
        # Delete bill items first
        cursor.execute('DELETE FROM bill_items WHERE bill_id = ?', (bill_id,))
        
        # Delete bill
        cursor.execute('DELETE FROM bills WHERE id = ?', (bill_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': 'Bill deleted successfully'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    try:
        conn = sqlite3.connect('billing.db')
        cursor = conn.cursor()
        
        # Total bills
        cursor.execute('SELECT COUNT(*) FROM bills')
        total_bills = cursor.fetchone()[0]
        
        # Total revenue
        cursor.execute('SELECT SUM(total_amount) FROM bills')
        total_revenue = cursor.fetchone()[0] or 0
        
        # Today's bills
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute('SELECT COUNT(*) FROM bills WHERE date = ?', (today,))
        today_bills = cursor.fetchone()[0]
        
        # Today's revenue
        cursor.execute('SELECT SUM(total_amount) FROM bills WHERE date = ?', (today,))
        today_revenue = cursor.fetchone()[0] or 0
        
        conn.close()
        
        return jsonify({
            'success': True,
            'stats': {
                'totalBills': total_bills,
                'totalRevenue': total_revenue,
                'todayBills': today_bills,
                'todayRevenue': today_revenue
            }
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    print("Starting Sri Amman Fushing Billing Software...")
    print("Access the application at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
